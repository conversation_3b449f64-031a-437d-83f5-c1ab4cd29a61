import React, { useRef, useEffect, useState } from "react";
import { ScrollView, View, Text, Image, NativeSyntheticEvent, NativeScrollEvent, requireNativeComponent, Dimensions } from "react-native";
import { BetterImage, SvgaPlayer, Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import signInThemeStyleAtom from "./theme";
import LinearGradient from "react-native-linear-gradient";
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import useThrottleCallback from 'hooks/useThrottleCallback';
import useDebounceUpdate from 'hooks/useDebounceUpdate';
import { updateWelfareAtom } from "atom/welfare";
import CompletedIcon from "../common/CompletedIcon";
import { AD_POSITION, AD_SOURCE, RewardType } from "constants/ad";
import watchAd from "utils/watchAd";
import { useSignInActions } from "hooks/useSignInActions";

import { FallbackReqType } from "constants/ad";
import { signInTaskAtom, writeSignInTaskAtom } from "./store";
import { reSignIn, SignInStatus } from "services/welfare";
import { rewardGoldCoin } from "services/welfare";
import dayjs from "dayjs";
import { Toast } from "@xmly/rn-sdk";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";
import LotteryModal, { useLotteryVideos } from "./LotteryModal";
import { useLotteryImages } from '../RewardModalContent';
import LottieView from 'lottie-react-native';
import { useLottieResource } from "hooks/useLottieResources";
import { signInLotteryLottie } from "constants/assets";
import { NativeModules } from 'react-native';
// import { Svga } from "components/Svga";
// const SVGAView = requireNativeComponent('SVGAView');

const todayBgIcon = 'https://imagev2.xmcdn.com/storages/c8ab-audiofreehighqps/74/EF/GAqh9sALwYE3AAAsdwOMxEtR.png';
const todayCompletedIcon = 'https://imagev2.xmcdn.com/storages/94a9-audiofreehighqps/C5/B6/GKwRIJILwYE4AAA33gOMxEuU.png';
const awardIcon = 'https://imagev2.xmcdn.com/storages/0ccd-audiofreehighqps/59/69/GKwRIDoLwYFRAAAyegOMxGCG.png';

export default function SignIn({ adHeight, setShowSignModal }: { adHeight: number | undefined, setShowSignModal: (show: boolean) => void }) {
  const theme = useAtomValue(signInThemeStyleAtom);
  const styles = getStyles(theme);
  const scrollRef = useRef<ScrollView>(null);
  const signInTask = useAtomValue(signInTaskAtom);
  const { awardInfo = [] } = signInTask || {};
  const querySignInTask = useSetAtom(writeSignInTaskAtom);
  const rewardGoldCoinHook = useRewardGoldCoin();

  // 直接在组件中使用 updateWelfareAtom 和 useDebounceUpdate
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const debouncedUpdateWelfare = useDebounceUpdate(updateWelfare);

  const todayIndex = awardInfo.findIndex(item => item.today);
  const todayAward = awardInfo[todayIndex];
  const lastAward = awardInfo[awardInfo.length - 1];
  const normalSigned = todayAward?.status === SignInStatus.COMPLETED;
  const signed = [SignInStatus.AD_COMPLETED].includes(todayAward?.status);
  const allCompleted = todayAward?.status === SignInStatus.LOTTERY;
  const adCoins = todayAward?.upgradeAward + (normalSigned ? 0 : todayAward?.award);
  const lotteryCompleted = awardInfo.filter(item => item.status > 0).length === awardInfo.length;
  const lotteryIcon = allCompleted ? theme.unSignIncIcon : awardIcon;
  const backdateIndex = awardInfo?.slice(0, todayIndex).findIndex(item => item.status === SignInStatus.INCOMPLETE);
  const adSignInBtnText = `我看广告签到${normalSigned ? '再' : ''}领${adCoins}金币`;
  const signInBtnText = `签到领${todayAward?.award}金币`;
  const [lastMarginRight, setLastMarginRight] = useState(10);
  const lotteryClickable = lotteryCompleted && !allCompleted;
  const lottieSource = useLottieResource(signInLotteryLottie);

  // 抽奖弹窗状态
  const [lotteryModalVisible, setLotteryModalVisible] = useState(false);
  const [lotteryCoins, setLotteryCoins] = useState(0);

  // 预加载抽奖视频资源
  const { load: preloadLotteryVideos } = useLotteryVideos();
  const preloadLotteryImages = useLotteryImages();

  // 图片是否已预加载，避免重复加载
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  // 在lotteryClickable为true时，预加载抽奖视频和图片
  useEffect(() => {
    if (lotteryClickable) {
      // 预加载视频
      console.log('预加载抽奖视频...');
      preloadLotteryVideos();
    }
  }, [lotteryClickable, preloadLotteryVideos]);

  useEffect(() => {
    // 预加载图片（只预加载一次）
    if (lotteryClickable && !imagesPreloaded) {
      Promise.all(preloadLotteryImages()).then(() => {
        setImagesPreloaded(true);
        console.log('奖励弹窗图片预加载完成');
      });
    }
  }, [lotteryClickable, imagesPreloaded, preloadLotteryImages]);

  useEffect(() => {
    if (todayIndex >= 0) {
      // 每个卡片宽度约为 71px，间距为 8px
      const cardWidth = 71 + 8;
      const scrollX = todayIndex * cardWidth;
      // 延迟执行以确保布局完成
      setTimeout(() => {
        scrollRef.current?.scrollTo({
          x: Math.max(0, scrollX - 100), // 100是预留的左侧空间
          animated: true
        });
      }, 100);
    }
  }, [todayIndex]);

  const handleSignIn = useThrottleCallback(async () => {
    clickReport(signInBtnText);
    await rewardGoldCoinHook({
      rewardType: RewardType.SIGN_IN,
      coins: todayAward?.award,
      sourceName: AD_SOURCE.SIGN_IN,
      fallbackReq: FallbackReqType.NORMAL,
    }, true);
    querySignInTask();
  });

  const { handleAdSignInAction, handleReSignInAction } = useSignInActions();

  const handleAdSignIn = useThrottleCallback(async () => {
    clickReport(adSignInBtnText);
    handleAdSignInAction(adCoins);

    // const res = await watchAd({
    //   sourceName: AD_SOURCE.AD_SIGN_IN,
    //   positionName: AD_POSITION.positionName,
    //   slotId: AD_POSITION.slotId,
    //   rewardType: RewardType.AD_SIGN_IN,
    //   coins: adCoins,
    // });
    // if (res.success) {
    //   await rewardGoldCoinHook({
    //     coins: adCoins,
    //     rewardType: RewardType.AD_SIGN_IN,
    //     sourceName: AD_SOURCE.AD_SIGN_IN,
    //     adId: res.adId,
    //     adResponseId: res.adResponseId,
    //     encryptType: res.encryptType,
    //     ecpm: res.ecpm,
    //     fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
    //   });
    //   querySignInTask();
    // }
  });

  const handleReSignIn = useThrottleCallback(async (reSignInDay: number = 0) => {
    clickReport(reSignInDay > 0 ? '补' : '去补签');
    if (!signInTask?.enableResignIn) {
      return Toast.info('每天只能补签一次');
    }

    handleReSignInAction(reSignInDay);
    
    // const res = await watchAd({
    //   sourceName: AD_SOURCE.AD_SIGN_IN,
    //   positionName: AD_POSITION.positionName,
    //   slotId: AD_POSITION.slotId,
    //   rewardType: RewardType.AD_SIGN_IN,
    // });
    // if (res.success) {
    //   const result = await reSignIn({
    //     adId: res?.adId,
    //     adResponseId: res?.adResponseId,
    //     encryptType: res.encryptType,
    //     ecpm: res.ecpm,
    //     fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
    //     reSignInDay
    //   });
    //   if (result?.data?.success) {
    //     Toast.info('补签成功');
    //   } else {
    //     Toast.info(result?.data?.toast || '补签失败');
    //   }
    //   querySignInTask();
    // }
  });

  const handleLottery = useThrottleCallback(async () => {
    try {
      // 直接调用 rewardGoldCoin 服务，不使用 hook
      const result = await rewardGoldCoin({
        rewardType: RewardType.SIGN_IN_LOTTERY,
        coins: 0,
        sourceName: AD_SOURCE.SIGN_IN_LOTTERY,
        fallbackReq: FallbackReqType.NORMAL,
      });

      // 获取奖励金币数量
      if (result?.data?.success) {
        const coins = result.data.coins || 0;

        // 显示抽奖弹窗
        setLotteryCoins(coins);
        setLotteryModalVisible(true);

        // 更新签到任务状态
        //querySignInTask();
      } else if (result?.data?.toast) {
        Toast.info(result.data.toast);
      }
    } catch (error) {
      console.error('抽奖失败:', error);
      Toast.info('抽奖失败，请稍后重试');
    }
  });

  useEffect(() => {
    querySignInTask();
  }, []);

  function onShow() {
    // 福利中心-签到模块  控件曝光
    xmlog.event(67702, 'slipPage', { currPage: 'welfareCenter' });
  }

  function clickReport(btnText: string) {
    // 福利中心-签到模块  点击事件
    xmlog.click(67701, 'SignInTask', { currPage: 'welfareCenter', Item: btnText });
  }

  function onLotteryBtnClick() {
    clickReport('立即抽奖');
    handleLottery();
  }

  function onLotteryIconClick() {
    clickReport('抽奖');
    handleLottery();
  }

  function handleLayout(e: any) {
    console.log('handleLayout', e.nativeEvent.layout.width);
    const scrollWidth = e.nativeEvent.layout.width;
    setLastMarginRight(Math.max(10, (scrollWidth - 60 * 6) / 6));
  }

  // 关闭抽奖弹窗
  const handleCloseLotteryModal = () => {
    querySignInTask();
    setLotteryModalVisible(false);

    // 在弹窗关闭后更新welfare状态
    const updateWelfareState = async () => {
      try {
        // 直接使用组件中的 debouncedUpdateWelfare
        await debouncedUpdateWelfare();

        // 更新签到任务状态
        querySignInTask();
      } catch (error) {
        console.error('更新福利状态失败:', error);
      }
    };

    updateWelfareState();
  };

  const showSignInViewOfLottery = todayAward !== undefined && todayAward.today && todayAward.hasLottery && todayAward.status != SignInStatus.LOTTERY;
  const showSignInViewOfAd = todayAward !== undefined && todayAward.today && (todayAward.status != SignInStatus.AD_COMPLETED && todayAward.status != SignInStatus.LOTTERY);

  const visible = showSignInViewOfLottery || showSignInViewOfAd || adHeight === 0 || [SignInStatus.INCOMPLETE].includes(todayAward?.status);
  console.log('signIn view visible=', visible, ",adHeight=", adHeight, ",showSignInViewOfLottery=", showSignInViewOfLottery, ",showSignInViewOfAd=", showSignInViewOfAd, ",todayAward=", todayAward);

  if (!visible) {
    setShowSignModal(false);
    return null;
  }

  setShowSignModal(true);

  return (
    <ScrollAnalyticComp
      itemKey="SignInTask"
      onShow={onShow}
    >
      <View style={styles.signInProgress}>
        {/* 签到进度部分 */}
        <LinearGradient
          colors={theme.headGradientColors}
          locations={[0, 1]}
          style={styles.progressHeader}>
          <Text style={styles.progressTitle}>{signInTask?.title}</Text>
        </LinearGradient>

        <View style={styles.progressDays}>
          <ScrollView
            ref={scrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            bounces={false}
            scrollEventThrottle={16}
            contentContainerStyle={styles.progressDaysScroll}
            onLayout={handleLayout}
          >
            <View style={styles.progressLine}></View>
            {awardInfo.slice(0, awardInfo.length - 1).map((item, index) => {
              const backdateAble = index < todayIndex && item.status === SignInStatus.INCOMPLETE;
              const isBackdate = index < todayIndex && item.status === SignInStatus.INCOMPLETE;
              const icon = isBackdate ? theme.backdateIcon : theme.unSignIncIcon;
              const todayCompleted = item.status > 0;
              const todayIcon = todayCompleted ? todayCompletedIcon : todayBgIcon;
              // const lastDayItemStyle = index === awardInfo.length - 2 ? styles.lastDayItem : null;
              const lastDayItemStyle = index === awardInfo.length - 2 ? { marginRight: lastMarginRight } : null;

              return (
                item.today ?
                  <View
                    style={[styles.dayItem, styles.todayItem, lastDayItemStyle]}
                    key={item.day}
                  >
                    <View style={styles.todayAward}>
                      <BetterImage
                        source={{
                          uri: todayIcon
                        }}
                        key={todayIcon}
                        quality={10}
                        imgHeight={60}
                        imgWidth={60}
                        style={[styles.icon, styles.todayIcon]}
                      />
                      {todayCompleted ? null : <Text style={[styles.dayText, { fontFamily: 'XmlyNumber' }]}>{item.award + item.upgradeAward}</Text>}
                    </View>
                    <Text style={[styles.dateText, styles.todayText]}>今天</Text>
                    {item.status > 0 ? <CompletedIcon style={styles.receivedIcon} /> : null}
                  </View> :
                  <Touch key={item.day} style={[styles.dayItem, lastDayItemStyle]} onPress={backdateAble ? () => handleReSignIn(item.day) : undefined}>
                    <BetterImage
                      source={{
                        uri: icon
                      }}
                      key={icon}
                      quality={10}
                      imgHeight={60}
                      imgWidth={60}
                      style={styles.icon}
                    />
                    <Text style={styles.dateText}>{dayjs(item.day).format('M月D日')}</Text>
                    {item.status > 0 ? <CompletedIcon style={styles.receivedIcon} /> : null}
                  </Touch>
              )
            })}
          </ScrollView>
          <BetterImage
            source={{
              uri: theme.shadow
            }}
            imgHeight={76}
            imgWidth={36}
            style={styles.shadow}
          />
          {/* )} */}
          {/* 抽奖 */}
          <Touch style={[styles.dayItem, styles.lotteryItem, { opacity: 1 }]} disabled={!lotteryClickable} onPress={onLotteryIconClick}>
            {lotteryClickable && lottieSource ?
              <LottieView
                source={lottieSource}
                style={[styles.icon, styles.giftIcon]}
                loop={true}
                autoPlay={true}
              />
              :
              <BetterImage
                source={{
                  uri: lotteryIcon
                }}
                key={lotteryIcon}
                imgHeight={60}
                imgWidth={54}
                style={[styles.icon, styles.giftIcon]}
              />}
            <Text style={[styles.dateText, lastAward?.today ? styles.todayText : null]}>{lastAward?.today ? '今天' : dayjs(lastAward?.day).format('M月D日')}</Text>
            {allCompleted ? <CompletedIcon style={styles.receivedIcon} /> : null}
          </Touch>
        </View>
        {!signed && !lotteryCompleted && !allCompleted ? (
          <View style={styles.signInButtonWrapper}>
            <Touch style={styles.signInButton} onPress={handleAdSignIn}>
              <Text style={styles.signInText}>{adSignInBtnText}</Text>
            </Touch>
          </View>
        ) : null}
        {[SignInStatus.INCOMPLETE].includes(todayAward?.status) ? (
          <Touch style={styles.signInTextButton} onPress={handleSignIn}>
            <Text style={styles.bottomText}>{signInBtnText}</Text>
          </Touch>
        ) : null}
        {signed && signInTask?.enableResignIn && backdateIndex > -1 ? (
          <View style={styles.signInButtonWrapper}>
            <Touch style={styles.signInButton} onPress={() => handleReSignIn()}>
              <Text style={styles.signInText}>去补签</Text>
            </Touch>
          </View>
        ) : null}
        {lotteryCompleted && !allCompleted ? (
          <View style={styles.signInButtonWrapper}>
            <Touch style={styles.signInButton} onPress={onLotteryBtnClick}>
              <Text style={styles.signInText}>立即抽奖</Text>
            </Touch>
          </View>
        ) : null}
      </View>

      {/* 抽奖弹窗 */}
      <LotteryModal
        visible={lotteryModalVisible}
        onClose={handleCloseLotteryModal}
        coins={lotteryCoins}
      />
    </ScrollAnalyticComp>
  )
}
